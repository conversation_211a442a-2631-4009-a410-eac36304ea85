<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      width: 320px;
      font-family: 'Microsoft YaHei', sans-serif;
      color: #333;
      font-size: 12px;
    }

    /* 顶部操作区 */
    .operation-area {
      background: #fff;
      padding: 12px;
      border-bottom: 1px solid #eee;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }

    .title {
      font-size: 14px;
      font-weight: 600;
      color: #1a1a1a;
    }

    .status-badge {
      padding: 4px 8px;
      border-radius: 3px;
      font-size: 11px;
      font-weight: 500;
      color: white;
    }

    .status-badge.running { background: #4CAF50; }
    .status-badge.stopped { background: #f44336; }
    .status-badge.sleeping { background: #2196F3; }

    .control-buttons {
      display: flex;
      gap: 8px;
    }

    .btn {
      border: none;
      border-radius: 3px;
      cursor: pointer;
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 6px 12px;
      color: white;
      transition: all 0.2s;
    }

    .btn-start {
      background: #4CAF50;
      flex: 1.2;
    }

    .btn-stop {
      background: #f44336;
      flex: 1;
    }

    .btn:disabled {
      background: #9e9e9e !important;
      opacity: 0.6;
      cursor: not-allowed;
    }

    .btn svg {
      width: 14px;
      height: 14px;
    }

    /* 下部交互区 */
    .interaction-area {
      background: #f8f9fa;
      padding: 12px;
      display: flex;
      flex-direction: column;
      gap: 12px;
      min-height: calc(100vh - 90px); /* 确保有足够空间 */
    }

    /* 设置区域样式优化 */
    .settings-section {
      background: white;
      border-radius: 4px;
      padding: 10px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }

    /* 日志区域样式优化 */
    .logs-section {
      background: white;
      border-radius: 4px;
      padding: 10px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.05);
      flex-grow: 1; /* 让日志区域占据剩余空间 */
      display: flex;
      flex-direction: column;
    }

    /* 统计区域样式优化 */
    .stats-section {
      background: white;
      border-radius: 4px;
      padding: 8px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.05);
      margin-top: auto; /* 将统计区推到底部 */
    }

    .section {
      background: white;
      border-radius: 4px;
      padding: 10px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6px;
      padding-bottom: 4px;
      border-bottom: 1px solid #f0f0f0;
    }

    .section-title {
      font-size: 11px;
      font-weight: 500;
      color: #666;
    }

    /* 统计区域 */
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 8px;
    }

    .stat-item {
      background: #f8f9fa;
      padding: 6px;
      border-radius: 3px;
      text-align: center;
    }

    .stat-value {
      font-size: 15px;
      font-weight: 600;
      color: #2196F3;
    }

    .stat-label {
      font-size: 11px;
      color: #666;
      margin-top: 2px;
    }

    /* 输入区域 */
    .input-area {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .input-group {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .input-row {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .input-label {
      font-size: 11px;
      color: #666;
      white-space: nowrap;
      min-width: 56px; /* 固定标签宽度 */
    }

    input[type="number"], textarea {
      width: 100%;
      padding: 6px;
      border: 1px solid #e0e0e0;
      border-radius: 3px;
      font-size: 12px;
      outline: none;
    }

    input[type="number"]:focus, textarea:focus {
      border-color: #2196F3;
    }

    .time-inputs {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;
    }

    .time-input-wrapper {
      display: flex;
      align-items: center;
      flex: 1;
    }

    .time-input-wrapper input {
      width: 100%;
      padding: 4px 6px;
      border: 1px solid #e0e0e0;
      border-radius: 3px;
      font-size: 12px;
      outline: none;
    }

    .time-unit {
      margin-left: 4px;
      color: #666;
      font-size: 12px;
      white-space: nowrap;
    }

    .time-separator {
      color: #666;
      font-size: 12px;
      padding: 0 2px;
    }

    textarea {
      flex: 1;
      min-height: 60px;
    }

    .input-group {
      margin-bottom: 8px;
    }

    /* 日志区域 */
    #logArea {
      width: 100%;
      flex-grow: 1; /* 让文本框填充剩余空间 */
      min-height: 200px;
      padding: 8px;
      border: 1px solid #e0e0e0;
      border-radius: 3px;
      font-family: monospace;
      font-size: 11px;
      line-height: 1.4;
      resize: none;
      background: #f8f9fa;
      color: #333;
      white-space: pre-wrap;
    }

    #logArea:focus {
      outline: none;
      border-color: #2196F3;
    }

    /* 清空按钮样式 */
    .btn-clear {
      padding: 2px 6px;
      font-size: 10px;
      color: #666;
      background: none;
      border: 1px solid #ddd;
      border-radius: 2px;
      cursor: pointer;
    }

    .btn-clear:hover {
      background: #f5f5f5;
      border-color: #ccc;
    }

    /* 添加复位按钮样式 */
    .reset-button {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    /* 复选框样式 */
    .checkbox-wrapper {
      display: flex;
      align-items: center;
      gap: 6px;
      cursor: pointer;
      flex: 1;
    }

    .checkbox-wrapper input[type="checkbox"] {
      width: 16px;
      height: 16px;
      cursor: pointer;
    }

    .checkbox-text {
      font-size: 12px;
      color: #666;
      cursor: pointer;
    }
  </style>
</head>
<body>
  <!-- 顶部操作区 -->
  <div class="operation-area">
    <div class="header">
      <div class="title">自动跟进助手</div>
      <div id="status" class="status-badge stopped">未运行</div>
    </div>
    <div class="control-buttons">
      <button id="startBtn" class="btn btn-start">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M8 5v14l11-7z"/>
        </svg>
        开始运行
      </button>
      <button id="stopBtn" class="btn btn-stop" disabled>
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
        </svg>
        停止
      </button>
    </div>
  </div>

  <!-- 交互区 -->
  <div class="interaction-area">
    <!-- 设置区域 -->
    <div class="section settings-section">
      <div class="section-header">
        <div class="section-title">运行设置</div>
      </div>
      <div class="input-area">
        <div class="input-group">
          <div class="input-row">
            <div class="input-label">等待时间</div>
            <div class="time-inputs">
              <div class="time-input-wrapper">
                <input type="number" id="minWaitTime" min="0.1" max="60" step="0.1" value="0.1">
                <span class="time-unit">秒</span>
              </div>
              <span class="time-separator">~</span>
              <div class="time-input-wrapper">
                <input type="number" id="maxWaitTime" min="0.1" max="60" step="0.1" value="3">
                <span class="time-unit">秒</span>
              </div>
            </div>
          </div>
        </div>
        <div class="input-group">
          <div class="input-row">
            <div class="input-label">回复内容</div>
            <textarea id="messages" rows="3" placeholder="请输入要回复的内容，每行一条..."></textarea>
          </div>
        </div>
        <div class="input-group">
          <div class="input-row">
            <div class="input-label">智能填表</div>
            <label class="checkbox-wrapper">
              <input type="checkbox" id="autoFillForm" checked>
              <span class="checkbox-text">自动填充表单字段</span>
            </label>
          </div>
          <div class="input-row">
            <div class="input-label"></div>
            <label class="checkbox-wrapper">
              <input type="checkbox" id="forceRequired" checked>
              <span class="checkbox-text">强制填充必填字段（*号）</span>
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- 日志区域 -->
    <div class="section logs-section">
      <div class="section-header">
        <div class="section-title">运行日志</div>
        <button id="clearLogBtn" class="btn-clear">清空</button>
      </div>
      <textarea id="logArea" readonly></textarea>
    </div>

    <!-- 统计区域 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-value" id="currentSuccess">0</div>
          <div class="stat-label">本次成功</div>
        </div>
        <div class="stat-item">
          <div class="stat-value" id="currentFail">0</div>
          <div class="stat-label">本次失败</div>
        </div>
        <div class="stat-item">
          <div class="stat-value" id="totalSuccess">0</div>
          <div class="stat-label">历史成功</div>
        </div>
        <div class="stat-item">
          <div class="stat-value" id="historyTotal">0</div>
          <div class="stat-label">历史总数</div>
        </div>
      </div>
      <button id="resetStats" class="reset-button">
        <span class="reset-icon">🔄</span>
        复位统计
      </button>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html> 