# 线索等级随机选择修改说明

## 修改概述

根据用户要求，将线索等级选择框从固定选择改为随机选择，增加操作的随机性和真实性。

## 修改内容

### 1. 字段检测逻辑修改

**修改前：**
```javascript
// 特殊处理线索等级：如果不是30天就强制修改
if (field.name === '线索等级') {
  const isNot30Days = !value.includes('30天内跟进');
  if (isEmpty || isNot30Days) {
    fieldsToProcess.push(field);
    if (isNot30Days && !isEmpty) {
      Statistics.addLog(`🔄 线索等级当前为"${value}"，将修改为30天内跟进`);
    }
  }
}
```

**修改后：**
```javascript
// 特殊处理线索等级：总是随机选择
if (field.name === '线索等级') {
  fieldsToProcess.push(field);
  if (!isEmpty) {
    Statistics.addLog(`🎲 线索等级当前为"${value}"，将随机重新选择`);
  }
}
```

**改进点：**
- 不再固定选择30天内跟进
- 总是将线索等级加入处理队列
- 无论当前值是什么都会重新随机选择

### 2. 选择逻辑修改

**修改前：**
```javascript
// 优先查找包含"A"和"7天"的选项
selectedOption = Array.from(options).find(opt => {
  const text = opt.textContent.trim();
  return text.includes('A') && text.includes('7天');
});

// 如果没找到，查找包含"A"的选项
if (!selectedOption) {
  selectedOption = Array.from(options).find(opt => {
    const text = opt.textContent.trim();
    return text.includes('A（') || text.startsWith('A');
  });
}

// 如果还没找到，选择第一个非空选项
if (!selectedOption) {
  selectedOption = Array.from(options).find(opt => {
    const text = opt.textContent.trim();
    return text !== '' && text !== '请选择' && text !== '全部';
  });
}
```

**修改后：**
```javascript
// 随机选择线索等级
let selectedOption = null;

// 过滤出有效的选项（排除"请选择"、"全部"等）
const validOptions = Array.from(options).filter(opt => {
  const text = opt.textContent.trim();
  return text !== '' && text !== '请选择' && text !== '全部' && 
         (text.includes('H（') || text.includes('A（') || text.includes('B（'));
});

if (validOptions.length > 0) {
  // 随机选择一个有效选项
  const randomIndex = Math.floor(Math.random() * validOptions.length);
  selectedOption = validOptions[randomIndex];
  
  Statistics.addLog(`🎲 从${validOptions.length}个选项中随机选择第${randomIndex + 1}个`);
  
  // 显示所有可用选项
  const optionTexts = validOptions.map(opt => opt.textContent.trim());
  Statistics.addLog(`🎯 可选线索等级: ${optionTexts.join(', ')}`);
}
```

**改进点：**
- 不再优先选择A级
- 真正的随机选择算法
- 支持H、A、B三个等级的随机选择
- 详细的日志显示选择过程

### 3. 日志信息优化

**修改前：**
```javascript
Statistics.addLog(`🎯 线索等级选择: ${selectedOption.textContent.trim()}`);
Statistics.addLog(`✅ 线索等级选择成功: ${selectedOption.textContent.trim()}`);
```

**修改后：**
```javascript
Statistics.addLog(`🎲 随机选择线索等级: ${selectedText}`);
Statistics.addLog(`✅ 线索等级随机选择成功: ${selectedText}`);
```

**改进点：**
- 明确标识这是随机选择
- 使用🎲图标表示随机性
- 日志信息更加清晰

## 功能特点

### 1. 真正的随机性
- 使用 `Math.floor(Math.random() * validOptions.length)` 实现真正的随机选择
- 每次运行都可能选择不同的线索等级
- 支持H级（2天内跟进）、A级（7天内跟进）、B级（30天内跟进）

### 2. 智能过滤
- 自动排除"请选择"、"全部"等无效选项
- 只从真正的线索等级选项中选择
- 通过文本模式匹配识别有效选项

### 3. 详细日志
- 显示可用选项的数量
- 显示所有可选的线索等级
- 显示随机选择的结果
- 便于调试和监控

### 4. 业务逻辑适配
- 随机选择的线索等级会影响后续的跟进时间生成
- H级 → 2天内跟进时间
- A级 → 7天内跟进时间
- B级 → 30天内跟进时间

## 预期效果

### 修改前的固定行为
```
🔄 线索等级当前为"B（30天内跟进）"，将修改为30天内跟进
🎯 线索等级选择: A（7天内跟进）
✅ 线索等级选择成功: A（7天内跟进）
```

### 修改后的随机行为
```
🎲 线索等级当前为"B（30天内跟进）"，将随机重新选择
🎯 可选线索等级: H（2天内跟进）, A（7天内跟进）, B（30天内跟进）
🎲 从3个选项中随机选择第2个
🎲 随机选择线索等级: A（7天内跟进）
✅ 线索等级随机选择成功: A（7天内跟进）
```

或者：
```
🎲 线索等级当前为"A（7天内跟进）"，将随机重新选择
🎯 可选线索等级: H（2天内跟进）, A（7天内跟进）, B（30天内跟进）
🎲 从3个选项中随机选择第1个
🎲 随机选择线索等级: H（2天内跟进）
✅ 线索等级随机选择成功: H（2天内跟进）
```

## 业务价值

1. **增加随机性**：避免总是使用相同的线索等级
2. **提高真实性**：随机选择更像真实的人工操作
3. **业务多样性**：不同的线索等级会产生不同的跟进时间
4. **测试覆盖**：能够测试系统对不同线索等级的处理

## 技术特点

1. **算法简单**：使用标准的随机数生成算法
2. **容错机制**：有效选项过滤确保选择的都是有效值
3. **可观测性**：详细的日志便于监控和调试
4. **扩展性**：容易添加新的线索等级选项

这次修改实现了真正的线索等级随机选择，增加了操作的随机性和真实性，同时保持了系统的稳定性和可观测性。
