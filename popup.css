/* 添加复位按钮样式 */
.reset-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  width: 100%;
  margin-top: 10px;
  padding: 8px;
  background-color: #F56C6C;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.reset-button:hover {
  background-color: #E64242;
}

.reset-button:active {
  transform: scale(0.95);
  background-color: #D63030;
}

.reset-icon {
  font-size: 16px;
  transition: transform 0.3s ease;
}

.reset-button:hover .reset-icon {
  transform: rotate(180deg);
} 